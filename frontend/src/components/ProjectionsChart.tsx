import React, { useRef, useEffect, useState } from 'react';
import * as d3 from 'd3';
import { FinancialProjection } from '../types';
import TooltipInfo from './TooltipInfo';
import { getTooltipInfo } from '../data/tooltipInfo';
import styles from './ProjectionsChart.module.css';

interface ProjectionsChartProps {
  projections: FinancialProjection[];
  onChartClick?: (projection: FinancialProjection) => void;
}

const ProjectionsChart: React.FC<ProjectionsChartProps> = ({ 
  projections, 
  onChartClick 
}) => {
  const chartRef = useRef<SVGSVGElement>(null);
  const [selectedMetric, setSelectedMetric] = useState<'wealth' | 'income' | 'savings'>('wealth');
  const [hoveredData, setHoveredData] = useState<FinancialProjection | null>(null);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      notation: 'compact',
      maximumFractionDigits: 1
    }).format(amount);
  };

  useEffect(() => {
    if (!chartRef.current || !projections.length) return;

    const svg = d3.select(chartRef.current);
    svg.selectAll('*').remove();

    const margin = { top: 20, right: 30, bottom: 40, left: 70 };
    const width = 800 - margin.left - margin.right;
    const height = 400 - margin.top - margin.bottom;

    const g = svg
      .attr('width', width + margin.left + margin.right)
      .attr('height', height + margin.top + margin.bottom)
      .append('g')
      .attr('transform', `translate(${margin.left},${margin.top})`);

    // Scales
    const xScale = d3
      .scaleLinear()
      .domain(d3.extent(projections, d => d.year) as [number, number])
      .range([0, width]);

    const yScale = d3
      .scaleLinear()
      .domain([
        0,
        d3.max(projections, d => {
          switch (selectedMetric) {
            case 'wealth': return d.projectedWealth;
            case 'income': return d.projectedIncome;
            case 'savings': return d.projectedSavings;
            default: return d.projectedWealth;
          }
        }) as number * 1.1
      ])
      .range([height, 0]);

    // Line generator
    const line = d3
      .line<FinancialProjection>()
      .x(d => xScale(d.year))
      .y(d => {
        switch (selectedMetric) {
          case 'wealth': return yScale(d.projectedWealth);
          case 'income': return yScale(d.projectedIncome);
          case 'savings': return yScale(d.projectedSavings);
          default: return yScale(d.projectedWealth);
        }
      })
      .curve(d3.curveMonotoneX);

    // Add gradient definition
    const gradient = g
      .append('defs')
      .append('linearGradient')
      .attr('id', 'chartGradient')
      .attr('gradientUnits', 'userSpaceOnUse')
      .attr('x1', 0).attr('y1', height)
      .attr('x2', 0).attr('y2', 0);

    gradient
      .append('stop')
      .attr('offset', '0%')
      .attr('stop-color', 'rgba(102, 126, 234, 0.1)');

    gradient
      .append('stop')
      .attr('offset', '100%')
      .attr('stop-color', 'rgba(102, 126, 234, 0.8)');

    // Add area under the curve
    const area = d3
      .area<FinancialProjection>()
      .x(d => xScale(d.year))
      .y0(height)
      .y1(d => {
        switch (selectedMetric) {
          case 'wealth': return yScale(d.projectedWealth);
          case 'income': return yScale(d.projectedIncome);
          case 'savings': return yScale(d.projectedSavings);
          default: return yScale(d.projectedWealth);
        }
      })
      .curve(d3.curveMonotoneX);

    g.append('path')
      .datum(projections)
      .attr('fill', 'url(#chartGradient)')
      .attr('d', area);

    // Add the line
    g.append('path')
      .datum(projections)
      .attr('fill', 'none')
      .attr('stroke', '#667eea')
      .attr('stroke-width', 3)
      .attr('d', line)
      .style('filter', 'drop-shadow(0 2px 4px rgba(0,0,0,0.2))');

    // Add dots
    g.selectAll('.dot')
      .data(projections)
      .enter().append('circle')
      .attr('class', 'dot')
      .attr('cx', d => xScale(d.year))
      .attr('cy', d => {
        switch (selectedMetric) {
          case 'wealth': return yScale(d.projectedWealth);
          case 'income': return yScale(d.projectedIncome);
          case 'savings': return yScale(d.projectedSavings);
          default: return yScale(d.projectedWealth);
        }
      })
      .attr('r', 4)
      .attr('fill', '#667eea')
      .attr('stroke', '#ffffff')
      .attr('stroke-width', 2)
      .style('cursor', 'pointer')
      .on('mouseover', function(event, d) {
        d3.select(this)
          .transition()
          .duration(200)
          .attr('r', 6)
          .attr('fill', '#764ba2');
        setHoveredData(d);
      })
      .on('mouseout', function() {
        d3.select(this)
          .transition()
          .duration(200)
          .attr('r', 4)
          .attr('fill', '#667eea');
        setHoveredData(null);
      })
      .on('click', function(event, d) {
        if (onChartClick) {
          onChartClick(d);
        }
      });

    // Add axes
    g.append('g')
      .attr('transform', `translate(0,${height})`)
      .call(d3.axisBottom(xScale).tickFormat(d => `Year ${d}`))
      .selectAll('text')
      .style('fill', 'rgba(255, 255, 255, 0.8)');

    g.append('g')
      .call(d3.axisLeft(yScale).tickFormat(d => formatCurrency(d as number)))
      .selectAll('text')
      .style('fill', 'rgba(255, 255, 255, 0.8)');

    // Style axis lines
    g.selectAll('.domain')
      .style('stroke', 'rgba(255, 255, 255, 0.2)');

    g.selectAll('.tick line')
      .style('stroke', 'rgba(255, 255, 255, 0.1)');

  }, [projections, selectedMetric]);

  const getMetricValue = (projection: FinancialProjection, metric: string) => {
    switch (metric) {
      case 'wealth': return projection.projectedWealth;
      case 'income': return projection.projectedIncome;
      case 'savings': return projection.projectedSavings;
      default: return projection.projectedWealth;
    }
  };

  const getMetricLabel = (metric: string) => {
    switch (metric) {
      case 'wealth': return 'Projected Wealth';
      case 'income': return 'Projected Income';
      case 'savings': return 'Projected Savings';
      default: return 'Projected Wealth';
    }
  };

  return (
    <div className={styles.chartContainer} data-testid="projections-chart">
      <div className={styles.glassCard}>
        <div className={styles.cardHeader}>
          <TooltipInfo 
            content={getTooltipInfo('projectedWealth').content}
            title={getTooltipInfo('projectedWealth').title}
          >
            <h2>📈 Financial Projections</h2>
          </TooltipInfo>
          
          <div className={styles.metricSelector}>
            {['wealth', 'income', 'savings'].map(metric => (
              <TooltipInfo
                key={metric}
                content={getTooltipInfo(`projected${metric.charAt(0).toUpperCase() + metric.slice(1)}`).content}
                title={getTooltipInfo(`projected${metric.charAt(0).toUpperCase() + metric.slice(1)}`).title}
              >
                <button
                  className={`${styles.metricButton} ${selectedMetric === metric ? styles.active : ''}`}
                  onClick={() => setSelectedMetric(metric as 'wealth' | 'income' | 'savings')}
                  data-testid={`metric-${metric}`}
                >
                  {getMetricLabel(metric)}
                </button>
              </TooltipInfo>
            ))}
          </div>
        </div>
        
        <div className={styles.chartContent}>
          <svg ref={chartRef} className={styles.chart}></svg>
          
          {hoveredData && (
            <div className={styles.hoverInfo} data-testid="hover-info">
              <div className={styles.hoverCard}>
                <h4>Year {hoveredData.year}</h4>
                <div className={styles.hoverMetrics}>
                  <div className={styles.hoverMetric}>
                    <span className={styles.hoverLabel}>Wealth:</span>
                    <span className={styles.hoverValue}>
                      {formatCurrency(hoveredData.projectedWealth)}
                    </span>
                  </div>
                  <div className={styles.hoverMetric}>
                    <span className={styles.hoverLabel}>Income:</span>
                    <span className={styles.hoverValue}>
                      {formatCurrency(hoveredData.projectedIncome)}
                    </span>
                  </div>
                  <div className={styles.hoverMetric}>
                    <span className={styles.hoverLabel}>Savings:</span>
                    <span className={styles.hoverValue}>
                      {formatCurrency(hoveredData.projectedSavings)}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
        
        <div className={styles.chartLegend}>
          <div className={styles.legendItem}>
            <div className={styles.legendColor}></div>
            <TooltipInfo
              content={getTooltipInfo('trendLine').content}
              title={getTooltipInfo('trendLine').title}
            >
              <span>Projection Trend</span>
            </TooltipInfo>
          </div>
          <div className={styles.legendNote}>
            📁 Click any point for detailed analysis
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProjectionsChart;