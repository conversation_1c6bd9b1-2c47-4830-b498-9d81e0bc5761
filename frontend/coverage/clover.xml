<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1752779523870" clover="3.2.0">
  <project timestamp="1752779523870" name="All files">
    <metrics statements="963" coveredstatements="675" conditionals="479" coveredconditionals="281" methods="213" coveredmethods="151" elements="1655" coveredelements="1107" complexity="0" loc="963" ncloc="963" packages="6" files="19" classes="19"/>
    <package name="src">
      <metrics statements="35" coveredstatements="0" conditionals="36" coveredconditionals="0" methods="12" coveredmethods="0"/>
      <file name="App.tsx" path="/Users/<USER>/WebstormProjects/er/frontend/src/App.tsx">
        <metrics statements="35" coveredstatements="0" conditionals="36" coveredconditionals="0" methods="12" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="37" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="242" count="0" type="stmt"/>
        <line num="279" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components">
      <metrics statements="565" coveredstatements="434" conditionals="267" coveredconditionals="188" methods="152" coveredmethods="118"/>
      <file name="CalculatorPage.tsx" path="/Users/<USER>/WebstormProjects/er/frontend/src/components/CalculatorPage.tsx">
        <metrics statements="68" coveredstatements="65" conditionals="42" coveredconditionals="29" methods="14" coveredmethods="13"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="33" count="1" type="stmt"/>
        <line num="34" count="50" type="stmt"/>
        <line num="35" count="50" type="stmt"/>
        <line num="36" count="50" type="stmt"/>
        <line num="37" count="50" type="stmt"/>
        <line num="39" count="50" type="stmt"/>
        <line num="50" count="50" type="stmt"/>
        <line num="53" count="50" type="stmt"/>
        <line num="54" count="14" type="stmt"/>
        <line num="55" count="14" type="cond" truecount="1" falsecount="0"/>
        <line num="56" count="3" type="stmt"/>
        <line num="57" count="3" type="stmt"/>
        <line num="58" count="3" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="69" count="50" type="stmt"/>
        <line num="70" count="4" type="stmt"/>
        <line num="71" count="4" type="stmt"/>
        <line num="75" count="50" type="stmt"/>
        <line num="76" count="50" type="stmt"/>
        <line num="77" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="81" count="0" type="stmt"/>
        <line num="85" count="50" type="stmt"/>
        <line num="88" count="50" type="cond" truecount="0" falsecount="1"/>
        <line num="89" count="5" type="cond" truecount="1" falsecount="0"/>
        <line num="90" count="5" type="stmt"/>
        <line num="92" count="5" type="stmt"/>
        <line num="94" count="5" type="stmt"/>
        <line num="96" count="5" type="stmt"/>
        <line num="99" count="5" type="stmt"/>
        <line num="106" count="5" type="cond" truecount="3" falsecount="0"/>
        <line num="107" count="1" type="stmt"/>
        <line num="111" count="9" type="stmt"/>
        <line num="112" count="4" type="cond" truecount="2" falsecount="0"/>
        <line num="115" count="4" type="stmt"/>
        <line num="116" count="4" type="stmt"/>
        <line num="117" count="4" type="stmt"/>
        <line num="118" count="4" type="stmt"/>
        <line num="120" count="4" type="stmt"/>
        <line num="130" count="4" type="stmt"/>
        <line num="133" count="4" type="cond" truecount="1" falsecount="0"/>
        <line num="134" count="4" type="stmt"/>
        <line num="141" count="4" type="stmt"/>
        <line num="142" count="4" type="stmt"/>
        <line num="146" count="1" type="stmt"/>
        <line num="147" count="1" type="stmt"/>
        <line num="149" count="5" type="stmt"/>
        <line num="153" count="50" type="stmt"/>
        <line num="154" count="5" type="stmt"/>
        <line num="157" count="50" type="stmt"/>
        <line num="158" count="1" type="stmt"/>
        <line num="159" count="1" type="stmt"/>
        <line num="162" count="50" type="stmt"/>
        <line num="163" count="1" type="stmt"/>
        <line num="164" count="1" type="stmt"/>
        <line num="165" count="1" type="stmt"/>
        <line num="166" count="1" type="stmt"/>
        <line num="167" count="1" type="stmt"/>
        <line num="168" count="1" type="stmt"/>
        <line num="171" count="50" type="stmt"/>
        <line num="363" count="8" type="stmt"/>
        <line num="366" count="1" type="stmt"/>
        <line num="398" count="1" type="stmt"/>
      </file>
      <file name="DataClearingTab.tsx" path="/Users/<USER>/WebstormProjects/er/frontend/src/components/DataClearingTab.tsx">
        <metrics statements="58" coveredstatements="53" conditionals="35" coveredconditionals="31" methods="7" coveredmethods="7"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="10" count="64" type="stmt"/>
        <line num="11" count="64" type="stmt"/>
        <line num="12" count="64" type="stmt"/>
        <line num="13" count="64" type="stmt"/>
        <line num="14" count="64" type="stmt"/>
        <line num="16" count="64" type="stmt"/>
        <line num="17" count="13" type="stmt"/>
        <line num="18" count="13" type="stmt"/>
        <line num="20" count="13" type="stmt"/>
        <line num="21" count="13" type="stmt"/>
        <line num="22" count="13" type="cond" truecount="2" falsecount="0"/>
        <line num="23" count="12" type="stmt"/>
        <line num="25" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="28" count="0" type="stmt"/>
        <line num="30" count="13" type="stmt"/>
        <line num="34" count="64" type="stmt"/>
        <line num="35" count="5" type="cond" truecount="2" falsecount="1"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="40" count="5" type="stmt"/>
        <line num="41" count="5" type="stmt"/>
        <line num="43" count="5" type="cond" truecount="5" falsecount="0"/>
        <line num="44" count="1" type="stmt"/>
        <line num="45" count="1" type="stmt"/>
        <line num="48" count="4" type="stmt"/>
        <line num="49" count="4" type="stmt"/>
        <line num="51" count="4" type="stmt"/>
        <line num="52" count="4" type="stmt"/>
        <line num="63" count="4" type="stmt"/>
        <line num="65" count="4" type="cond" truecount="2" falsecount="0"/>
        <line num="66" count="3" type="stmt"/>
        <line num="67" count="3" type="stmt"/>
        <line num="69" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="72" count="0" type="stmt"/>
        <line num="74" count="4" type="stmt"/>
        <line num="78" count="64" type="stmt"/>
        <line num="79" count="39" type="stmt"/>
        <line num="80" count="39" type="cond" truecount="0" falsecount="1"/>
        <line num="81" count="39" type="stmt"/>
        <line num="89" count="64" type="stmt"/>
        <line num="90" count="2" type="stmt"/>
        <line num="91" count="2" type="stmt"/>
        <line num="92" count="2" type="stmt"/>
        <line num="93" count="2" type="stmt"/>
        <line num="96" count="64" type="cond" truecount="1" falsecount="0"/>
        <line num="97" count="30" type="stmt"/>
        <line num="129" count="34" type="cond" truecount="1" falsecount="0"/>
        <line num="130" count="31" type="stmt"/>
        <line num="149" count="7" type="stmt"/>
        <line num="173" count="7" type="stmt"/>
        <line num="215" count="3" type="cond" truecount="1" falsecount="0"/>
        <line num="216" count="3" type="stmt"/>
        <line num="245" count="0" type="stmt"/>
        <line num="248" count="1" type="stmt"/>
      </file>
      <file name="ErrorMessage.tsx" path="/Users/<USER>/WebstormProjects/er/frontend/src/components/ErrorMessage.tsx">
        <metrics statements="10" coveredstatements="10" conditionals="3" coveredconditionals="3" methods="4" coveredmethods="4"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="17" count="5" type="stmt"/>
        <line num="18" count="5" type="cond" truecount="1" falsecount="0"/>
        <line num="19" count="1" type="stmt"/>
        <line num="20" count="1" type="stmt"/>
        <line num="23" count="1" type="stmt"/>
        <line num="27" count="5" type="stmt"/>
        <line num="49" count="1" type="stmt"/>
      </file>
      <file name="FinancialProjectionTable.tsx" path="/Users/<USER>/WebstormProjects/er/frontend/src/components/FinancialProjectionTable.tsx">
        <metrics statements="36" coveredstatements="0" conditionals="14" coveredconditionals="0" methods="7" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="41" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="42" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="47" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="48" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="51" count="0" type="stmt"/>
        <line num="53" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="55" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="70" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="73" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="74" count="0" type="stmt"/>
        <line num="81" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="82" count="0" type="stmt"/>
        <line num="95" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="96" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
      </file>
      <file name="HistoricalDataTable.tsx" path="/Users/<USER>/WebstormProjects/er/frontend/src/components/HistoricalDataTable.tsx">
        <metrics statements="69" coveredstatements="56" conditionals="28" coveredconditionals="20" methods="30" coveredmethods="23"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="23" count="1" type="stmt"/>
        <line num="28" count="21" type="stmt"/>
        <line num="29" count="21" type="stmt"/>
        <line num="31" count="21" type="stmt"/>
        <line num="32" count="1074" type="stmt"/>
        <line num="40" count="21" type="stmt"/>
        <line num="41" count="1074" type="stmt"/>
        <line num="50" count="21" type="stmt"/>
        <line num="51" count="1137" type="cond" truecount="3" falsecount="1"/>
        <line num="52" count="558" type="stmt"/>
        <line num="53" count="540" type="stmt"/>
        <line num="54" count="39" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="59" count="21" type="stmt"/>
        <line num="60" count="1074" type="cond" truecount="3" falsecount="1"/>
        <line num="61" count="537" type="stmt"/>
        <line num="62" count="519" type="stmt"/>
        <line num="63" count="18" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="68" count="21" type="stmt"/>
        <line num="69" count="19" type="stmt"/>
        <line num="72" count="19" type="cond" truecount="0" falsecount="1"/>
        <line num="73" count="0" type="stmt"/>
        <line num="77" count="19" type="cond" truecount="0" falsecount="1"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="90" count="19" type="stmt"/>
        <line num="93" count="21" type="stmt"/>
        <line num="94" count="19" type="stmt"/>
        <line num="97" count="20" type="stmt"/>
        <line num="106" count="1074" type="stmt"/>
        <line num="116" count="1074" type="stmt"/>
        <line num="133" count="1074" type="stmt"/>
        <line num="138" count="20" type="stmt"/>
        <line num="147" count="1074" type="stmt"/>
        <line num="158" count="1074" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="184" count="21" type="stmt"/>
        <line num="196" count="21" type="stmt"/>
        <line num="197" count="21" type="stmt"/>
        <line num="199" count="1074" type="stmt"/>
        <line num="200" count="1074" type="stmt"/>
        <line num="201" count="1074" type="stmt"/>
        <line num="203" count="21" type="stmt"/>
        <line num="206" count="21" type="stmt"/>
        <line num="208" count="21" type="stmt"/>
        <line num="241" count="84" type="stmt"/>
        <line num="244" count="0" type="stmt"/>
        <line num="259" count="0" type="stmt"/>
        <line num="281" count="0" type="stmt"/>
        <line num="282" count="0" type="stmt"/>
        <line num="292" count="20" type="stmt"/>
        <line num="293" count="20" type="stmt"/>
        <line num="296" count="100" type="stmt"/>
        <line num="299" count="100" type="stmt"/>
        <line num="322" count="1074" type="stmt"/>
        <line num="323" count="1074" type="stmt"/>
        <line num="324" count="1074" type="stmt"/>
        <line num="329" count="0" type="stmt"/>
        <line num="332" count="5370" type="stmt"/>
        <line num="333" count="5370" type="stmt"/>
        <line num="362" count="1" type="stmt"/>
      </file>
      <file name="HistoryPage.tsx" path="/Users/<USER>/WebstormProjects/er/frontend/src/components/HistoryPage.tsx">
        <metrics statements="92" coveredstatements="91" conditionals="28" coveredconditionals="25" methods="34" coveredmethods="34"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="41" count="1" type="stmt"/>
        <line num="42" count="71" type="stmt"/>
        <line num="43" count="71" type="stmt"/>
        <line num="44" count="71" type="stmt"/>
        <line num="49" count="71" type="stmt"/>
        <line num="50" count="71" type="stmt"/>
        <line num="51" count="71" type="stmt"/>
        <line num="54" count="71" type="stmt"/>
        <line num="55" count="18" type="stmt"/>
        <line num="59" count="71" type="stmt"/>
        <line num="60" count="42" type="stmt"/>
        <line num="63" count="71" type="stmt"/>
        <line num="64" count="18" type="stmt"/>
        <line num="65" count="18" type="stmt"/>
        <line num="66" count="18" type="stmt"/>
        <line num="69" count="18" type="stmt"/>
        <line num="70" count="18" type="cond" truecount="1" falsecount="0"/>
        <line num="71" count="2" type="stmt"/>
        <line num="72" count="2" type="stmt"/>
        <line num="73" count="2" type="stmt"/>
        <line num="89" count="18" type="stmt"/>
        <line num="90" count="18" type="stmt"/>
        <line num="91" count="36" type="stmt"/>
        <line num="111" count="18" type="stmt"/>
        <line num="112" count="18" type="stmt"/>
        <line num="113" count="36" type="stmt"/>
        <line num="133" count="18" type="stmt"/>
        <line num="155" count="164" type="stmt"/>
        <line num="157" count="18" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="161" count="18" type="stmt"/>
        <line num="165" count="71" type="stmt"/>
        <line num="166" count="42" type="stmt"/>
        <line num="169" count="42" type="stmt"/>
        <line num="170" count="42" type="stmt"/>
        <line num="178" count="42" type="cond" truecount="1" falsecount="0"/>
        <line num="179" count="2" type="stmt"/>
        <line num="180" count="10" type="stmt"/>
        <line num="184" count="42" type="cond" truecount="1" falsecount="0"/>
        <line num="185" count="10" type="stmt"/>
        <line num="189" count="42" type="cond" truecount="1" falsecount="0"/>
        <line num="190" count="5" type="stmt"/>
        <line num="193" count="42" type="stmt"/>
        <line num="196" count="71" type="stmt"/>
        <line num="197" count="1" type="stmt"/>
        <line num="198" count="6" type="stmt"/>
        <line num="201" count="71" type="stmt"/>
        <line num="202" count="1" type="stmt"/>
        <line num="208" count="1" type="stmt"/>
        <line num="209" count="1" type="stmt"/>
        <line num="210" count="1" type="stmt"/>
        <line num="211" count="1" type="stmt"/>
        <line num="212" count="1" type="stmt"/>
        <line num="213" count="1" type="stmt"/>
        <line num="214" count="1" type="stmt"/>
        <line num="215" count="1" type="stmt"/>
        <line num="216" count="1" type="stmt"/>
        <line num="219" count="71" type="stmt"/>
        <line num="220" count="152" type="stmt"/>
        <line num="226" count="152" type="cond" truecount="1" falsecount="1"/>
        <line num="229" count="71" type="stmt"/>
        <line num="230" count="149" type="cond" truecount="1" falsecount="0"/>
        <line num="232" count="3" type="stmt"/>
        <line num="238" count="3" type="cond" truecount="1" falsecount="1"/>
        <line num="241" count="71" type="stmt"/>
        <line num="242" count="149" type="cond" truecount="1" falsecount="0"/>
        <line num="244" count="146" type="stmt"/>
        <line num="250" count="146" type="stmt"/>
        <line num="251" count="146" type="cond" truecount="1" falsecount="1"/>
        <line num="254" count="71" type="stmt"/>
        <line num="255" count="3" type="stmt"/>
        <line num="256" count="3" type="stmt"/>
        <line num="259" count="71" type="cond" truecount="1" falsecount="0"/>
        <line num="260" count="18" type="stmt"/>
        <line num="263" count="53" type="stmt"/>
        <line num="286" count="2" type="stmt"/>
        <line num="302" count="2" type="stmt"/>
        <line num="318" count="1" type="stmt"/>
        <line num="359" count="149" type="stmt"/>
        <line num="365" count="149" type="stmt"/>
        <line num="371" count="149" type="stmt"/>
        <line num="386" count="149" type="stmt"/>
        <line num="389" count="3" type="stmt"/>
        <line num="446" count="1" type="stmt"/>
        <line num="447" count="1" type="stmt"/>
        <line num="452" count="1" type="stmt"/>
        <line num="488" count="1" type="stmt"/>
      </file>
      <file name="LoadingSpinner.tsx" path="/Users/<USER>/WebstormProjects/er/frontend/src/components/LoadingSpinner.tsx">
        <metrics statements="4" coveredstatements="4" conditionals="4" coveredconditionals="4" methods="1" coveredmethods="1"/>
        <line num="2" count="3" type="stmt"/>
        <line num="9" count="3" type="stmt"/>
        <line num="13" count="23" type="stmt"/>
        <line num="30" count="3" type="stmt"/>
      </file>
      <file name="MilestoneProjectionsTab.tsx" path="/Users/<USER>/WebstormProjects/er/frontend/src/components/MilestoneProjectionsTab.tsx">
        <metrics statements="43" coveredstatements="42" conditionals="15" coveredconditionals="14" methods="11" coveredmethods="11"/>
        <line num="1" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="26" count="1" type="stmt"/>
        <line num="27" count="61" type="stmt"/>
        <line num="28" count="61" type="stmt"/>
        <line num="29" count="61" type="stmt"/>
        <line num="30" count="61" type="stmt"/>
        <line num="31" count="61" type="stmt"/>
        <line num="33" count="61" type="stmt"/>
        <line num="34" count="21" type="stmt"/>
        <line num="35" count="21" type="stmt"/>
        <line num="37" count="21" type="stmt"/>
        <line num="38" count="21" type="stmt"/>
        <line num="39" count="21" type="stmt"/>
        <line num="40" count="20" type="stmt"/>
        <line num="42" count="20" type="cond" truecount="2" falsecount="0"/>
        <line num="43" count="17" type="stmt"/>
        <line num="45" count="3" type="cond" truecount="1" falsecount="1"/>
        <line num="48" count="0" type="stmt"/>
        <line num="50" count="20" type="stmt"/>
        <line num="54" count="61" type="stmt"/>
        <line num="55" count="18" type="stmt"/>
        <line num="58" count="61" type="stmt"/>
        <line num="59" count="3" type="stmt"/>
        <line num="62" count="61" type="stmt"/>
        <line num="63" count="210" type="stmt"/>
        <line num="71" count="61" type="stmt"/>
        <line num="73" count="168" type="stmt"/>
        <line num="74" count="168" type="stmt"/>
        <line num="77" count="61" type="stmt"/>
        <line num="78" count="42" type="stmt"/>
        <line num="81" count="61" type="cond" truecount="3" falsecount="0"/>
        <line num="82" count="19" type="stmt"/>
        <line num="89" count="42" type="cond" truecount="3" falsecount="0"/>
        <line num="90" count="3" type="stmt"/>
        <line num="102" count="39" type="cond" truecount="1" falsecount="0"/>
        <line num="103" count="18" type="stmt"/>
        <line num="110" count="21" type="stmt"/>
        <line num="128" count="2" type="stmt"/>
        <line num="143" count="2" type="stmt"/>
        <line num="171" count="42" type="stmt"/>
        <line num="172" count="126" type="stmt"/>
        <line num="213" count="1" type="stmt"/>
      </file>
      <file name="Navigation.tsx" path="/Users/<USER>/WebstormProjects/er/frontend/src/components/Navigation.tsx">
        <metrics statements="14" coveredstatements="12" conditionals="9" coveredconditionals="8" methods="9" coveredmethods="7"/>
        <line num="10" count="1" type="stmt"/>
        <line num="11" count="19" type="stmt"/>
        <line num="12" count="4" type="cond" truecount="3" falsecount="0"/>
        <line num="13" count="3" type="stmt"/>
        <line num="14" count="3" type="stmt"/>
        <line num="18" count="19" type="stmt"/>
        <line num="23" count="2" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="34" count="5" type="stmt"/>
        <line num="35" count="4" type="stmt"/>
        <line num="45" count="2" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="56" count="1" type="stmt"/>
        <line num="66" count="1" type="stmt"/>
      </file>
      <file name="ProjectionsChart.tsx" path="/Users/<USER>/WebstormProjects/er/frontend/src/components/ProjectionsChart.tsx">
        <metrics statements="81" coveredstatements="23" conditionals="32" coveredconditionals="9" methods="20" coveredmethods="4"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="17" count="23" type="stmt"/>
        <line num="18" count="23" type="stmt"/>
        <line num="19" count="23" type="stmt"/>
        <line num="21" count="23" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="30" count="23" type="stmt"/>
        <line num="31" count="23" type="cond" truecount="3" falsecount="0"/>
        <line num="33" count="22" type="stmt"/>
        <line num="34" count="22" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="57" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="72" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="106" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="107" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="136" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="165" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="166" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="191" count="23" type="stmt"/>
        <line num="192" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="193" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="200" count="23" type="stmt"/>
        <line num="201" count="69" type="cond" truecount="3" falsecount="1"/>
        <line num="202" count="23" type="stmt"/>
        <line num="203" count="23" type="stmt"/>
        <line num="204" count="23" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="209" count="23" type="stmt"/>
        <line num="222" count="69" type="stmt"/>
        <line num="229" count="0" type="stmt"/>
        <line num="290" count="1" type="stmt"/>
      </file>
      <file name="TooltipInfo.tsx" path="/Users/<USER>/WebstormProjects/er/frontend/src/components/TooltipInfo.tsx">
        <metrics statements="54" coveredstatements="43" conditionals="18" coveredconditionals="13" methods="6" coveredmethods="6"/>
        <line num="1" count="3" type="stmt"/>
        <line num="2" count="3" type="stmt"/>
        <line num="12" count="3" type="stmt"/>
        <line num="19" count="431" type="stmt"/>
        <line num="20" count="431" type="stmt"/>
        <line num="21" count="431" type="stmt"/>
        <line num="22" count="431" type="stmt"/>
        <line num="23" count="431" type="stmt"/>
        <line num="25" count="431" type="stmt"/>
        <line num="26" count="13" type="cond" truecount="1" falsecount="0"/>
        <line num="27" count="2" type="stmt"/>
        <line num="30" count="13" type="stmt"/>
        <line num="31" count="9" type="stmt"/>
        <line num="32" count="9" type="cond" truecount="1" falsecount="0"/>
        <line num="33" count="9" type="stmt"/>
        <line num="34" count="9" type="stmt"/>
        <line num="37" count="9" type="stmt"/>
        <line num="38" count="9" type="stmt"/>
        <line num="39" count="9" type="stmt"/>
        <line num="40" count="9" type="stmt"/>
        <line num="42" count="9" type="cond" truecount="1" falsecount="3"/>
        <line num="44" count="9" type="stmt"/>
        <line num="45" count="9" type="stmt"/>
        <line num="46" count="9" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="62" count="9" type="cond" truecount="0" falsecount="1"/>
        <line num="63" count="0" type="stmt"/>
        <line num="65" count="9" type="cond" truecount="1" falsecount="0"/>
        <line num="66" count="9" type="stmt"/>
        <line num="68" count="9" type="cond" truecount="0" falsecount="1"/>
        <line num="69" count="0" type="stmt"/>
        <line num="71" count="9" type="cond" truecount="1" falsecount="0"/>
        <line num="72" count="9" type="stmt"/>
        <line num="75" count="9" type="stmt"/>
        <line num="76" count="9" type="stmt"/>
        <line num="81" count="431" type="stmt"/>
        <line num="82" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="83" count="2" type="stmt"/>
        <line num="84" count="2" type="stmt"/>
        <line num="86" count="2" type="stmt"/>
        <line num="89" count="431" type="stmt"/>
        <line num="90" count="135" type="stmt"/>
        <line num="91" count="135" type="cond" truecount="1" falsecount="0"/>
        <line num="92" count="9" type="stmt"/>
        <line num="97" count="431" type="stmt"/>
        <line num="137" count="3" type="stmt"/>
      </file>
      <file name="WealthAnalysisDisplay.tsx" path="/Users/<USER>/WebstormProjects/er/frontend/src/components/WealthAnalysisDisplay.tsx">
        <metrics statements="13" coveredstatements="13" conditionals="8" coveredconditionals="6" methods="5" coveredmethods="5"/>
        <line num="3" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="10" count="12" type="stmt"/>
        <line num="11" count="108" type="stmt"/>
        <line num="19" count="12" type="stmt"/>
        <line num="20" count="12" type="stmt"/>
        <line num="23" count="12" type="stmt"/>
        <line num="24" count="48" type="stmt"/>
        <line num="32" count="48" type="cond" truecount="1" falsecount="1"/>
        <line num="35" count="12" type="stmt"/>
        <line num="91" count="48" type="stmt"/>
        <line num="92" count="48" type="stmt"/>
        <line num="152" count="1" type="stmt"/>
      </file>
      <file name="WealthTrackerForm.tsx" path="/Users/<USER>/WebstormProjects/er/frontend/src/components/WealthTrackerForm.tsx">
        <metrics statements="23" coveredstatements="22" conditionals="31" coveredconditionals="26" methods="4" coveredmethods="3"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="27" count="1" type="stmt"/>
        <line num="28" count="17" type="stmt"/>
        <line num="29" count="17" type="stmt"/>
        <line num="31" count="17" type="stmt"/>
        <line num="33" count="17" type="stmt"/>
        <line num="34" count="2" type="stmt"/>
        <line num="36" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="37" count="1" type="stmt"/>
        <line num="42" count="1" type="stmt"/>
        <line num="44" count="1" type="stmt"/>
        <line num="53" count="1" type="stmt"/>
        <line num="56" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="57" count="2" type="stmt"/>
        <line num="58" count="2" type="stmt"/>
        <line num="62" count="17" type="cond" truecount="2" falsecount="0"/>
        <line num="64" count="17" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="81" count="2" type="stmt"/>
        <line num="297" count="1" type="stmt"/>
      </file>
    </package>
    <package name="src.data">
      <metrics statements="3" coveredstatements="3" conditionals="2" coveredconditionals="2" methods="1" coveredmethods="1"/>
      <file name="tooltipInfo.ts" path="/Users/<USER>/WebstormProjects/er/frontend/src/data/tooltipInfo.ts">
        <metrics statements="3" coveredstatements="3" conditionals="2" coveredconditionals="2" methods="1" coveredmethods="1"/>
        <line num="8" count="3" type="stmt"/>
        <line num="175" count="3" type="stmt"/>
        <line num="176" count="313" type="cond" truecount="2" falsecount="0"/>
      </file>
    </package>
    <package name="src.hooks">
      <metrics statements="99" coveredstatements="50" conditionals="32" coveredconditionals="9" methods="8" coveredmethods="4"/>
      <file name="useWealthTracker.ts" path="/Users/<USER>/WebstormProjects/er/frontend/src/hooks/useWealthTracker.ts">
        <metrics statements="99" coveredstatements="50" conditionals="32" coveredconditionals="9" methods="8" coveredmethods="4"/>
        <line num="1" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="37" count="1" type="stmt"/>
        <line num="38" count="23" type="stmt"/>
        <line num="39" count="23" type="stmt"/>
        <line num="40" count="23" type="stmt"/>
        <line num="42" count="23" type="stmt"/>
        <line num="43" count="23" type="stmt"/>
        <line num="44" count="23" type="stmt"/>
        <line num="45" count="23" type="stmt"/>
        <line num="46" count="23" type="stmt"/>
        <line num="48" count="23" type="stmt"/>
        <line num="50" count="23" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="54" count="23" type="stmt"/>
        <line num="55" count="4" type="stmt"/>
        <line num="56" count="4" type="stmt"/>
        <line num="58" count="4" type="stmt"/>
        <line num="59" count="4" type="stmt"/>
        <line num="61" count="4" type="cond" truecount="2" falsecount="0"/>
        <line num="63" count="3" type="stmt"/>
        <line num="64" count="3" type="stmt"/>
        <line num="66" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="67" count="1" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="73" count="4" type="stmt"/>
        <line num="77" count="23" type="stmt"/>
        <line num="78" count="3" type="stmt"/>
        <line num="79" count="3" type="stmt"/>
        <line num="81" count="3" type="stmt"/>
        <line num="82" count="3" type="stmt"/>
        <line num="84" count="3" type="cond" truecount="2" falsecount="0"/>
        <line num="86" count="2" type="stmt"/>
        <line num="87" count="2" type="stmt"/>
        <line num="89" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="90" count="1" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="96" count="3" type="stmt"/>
        <line num="100" count="23" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="107" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="111" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="112" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="122" count="23" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="129" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="133" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="134" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="144" count="23" type="stmt"/>
        <line num="145" count="6" type="stmt"/>
        <line num="146" count="6" type="stmt"/>
        <line num="148" count="6" type="stmt"/>
        <line num="149" count="6" type="stmt"/>
        <line num="151" count="6" type="cond" truecount="3" falsecount="1"/>
        <line num="152" count="6" type="stmt"/>
        <line num="153" count="6" type="cond" truecount="0" falsecount="1"/>
        <line num="154" count="0" type="stmt"/>
        <line num="156" count="6" type="cond" truecount="0" falsecount="1"/>
        <line num="157" count="0" type="stmt"/>
        <line num="159" count="6" type="stmt"/>
        <line num="161" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="162" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="168" count="6" type="stmt"/>
        <line num="172" count="23" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="179" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="180" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="185" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="186" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="196" count="23" type="stmt"/>
      </file>
    </package>
    <package name="src.services">
      <metrics statements="216" coveredstatements="143" conditionals="126" coveredconditionals="66" methods="33" coveredmethods="21"/>
      <file name="apiService.ts" path="/Users/<USER>/WebstormProjects/er/frontend/src/services/apiService.ts">
        <metrics statements="143" coveredstatements="86" conditionals="85" coveredconditionals="41" methods="22" coveredmethods="11"/>
        <line num="1" count="1" type="stmt"/>
        <line num="12" count="1" type="cond" truecount="2" falsecount="0"/>
        <line num="13" count="1" type="stmt"/>
        <line num="16" count="1" type="stmt"/>
        <line num="25" count="1" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="37" count="1" type="stmt"/>
        <line num="38" count="17" type="cond" truecount="3" falsecount="0"/>
        <line num="39" count="1" type="stmt"/>
        <line num="41" count="16" type="cond" truecount="3" falsecount="0"/>
        <line num="42" count="3" type="stmt"/>
        <line num="44" count="13" type="cond" truecount="2" falsecount="1"/>
        <line num="45" count="0" type="stmt"/>
        <line num="49" count="1" type="stmt"/>
        <line num="50" count="2" type="cond" truecount="2" falsecount="1"/>
        <line num="51" count="0" type="stmt"/>
        <line num="53" count="2" type="cond" truecount="2" falsecount="1"/>
        <line num="54" count="0" type="stmt"/>
        <line num="56" count="2" type="stmt"/>
        <line num="57" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="58" count="1" type="stmt"/>
        <line num="60" count="1" type="stmt"/>
        <line num="63" count="1" type="stmt"/>
        <line num="64" count="2" type="cond" truecount="2" falsecount="1"/>
        <line num="65" count="0" type="stmt"/>
        <line num="67" count="2" type="stmt"/>
        <line num="68" count="1" type="stmt"/>
        <line num="69" count="1" type="stmt"/>
        <line num="70" count="1" type="stmt"/>
        <line num="72" count="1" type="cond" truecount="2" falsecount="1"/>
        <line num="73" count="0" type="stmt"/>
        <line num="78" count="1" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="85" count="0" type="stmt"/>
        <line num="87" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="88" count="0" type="stmt"/>
        <line num="90" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="91" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="98" count="1" type="stmt"/>
        <line num="101" count="1" type="stmt"/>
        <line num="102" count="1" type="stmt"/>
        <line num="103" count="1" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="184" count="2" type="stmt"/>
        <line num="186" count="2" type="stmt"/>
        <line num="189" count="1" type="stmt"/>
        <line num="199" count="1" type="stmt"/>
        <line num="200" count="1" type="stmt"/>
        <line num="206" count="1" type="stmt"/>
        <line num="215" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="229" count="2" type="stmt"/>
        <line num="231" count="2" type="stmt"/>
        <line num="233" count="1" type="stmt"/>
        <line num="242" count="1" type="stmt"/>
        <line num="243" count="1" type="stmt"/>
        <line num="249" count="1" type="stmt"/>
        <line num="258" count="2" type="stmt"/>
        <line num="259" count="2" type="stmt"/>
        <line num="260" count="1" type="stmt"/>
        <line num="261" count="1" type="stmt"/>
        <line num="263" count="1" type="stmt"/>
        <line num="273" count="0" type="stmt"/>
        <line num="274" count="0" type="stmt"/>
        <line num="275" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="277" count="0" type="stmt"/>
        <line num="278" count="0" type="stmt"/>
        <line num="291" count="0" type="stmt"/>
        <line num="296" count="0" type="stmt"/>
        <line num="298" count="0" type="stmt"/>
        <line num="307" count="2" type="stmt"/>
        <line num="308" count="2" type="stmt"/>
        <line num="309" count="1" type="stmt"/>
        <line num="310" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="312" count="1" type="stmt"/>
        <line num="313" count="1" type="stmt"/>
        <line num="314" count="5" type="stmt"/>
        <line num="322" count="1" type="stmt"/>
        <line num="327" count="0" type="stmt"/>
        <line num="329" count="1" type="stmt"/>
        <line num="339" count="2" type="stmt"/>
        <line num="340" count="2" type="stmt"/>
        <line num="345" count="1" type="stmt"/>
        <line num="350" count="1" type="stmt"/>
        <line num="355" count="1" type="stmt"/>
        <line num="365" count="1" type="stmt"/>
        <line num="366" count="1" type="stmt"/>
        <line num="367" count="1" type="stmt"/>
        <line num="369" count="0" type="stmt"/>
        <line num="387" count="3" type="stmt"/>
        <line num="389" count="3" type="cond" truecount="2" falsecount="1"/>
        <line num="390" count="0" type="stmt"/>
        <line num="393" count="3" type="stmt"/>
        <line num="395" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="396" count="1" type="stmt"/>
        <line num="399" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="400" count="1" type="stmt"/>
        <line num="403" count="2" type="cond" truecount="0" falsecount="1"/>
        <line num="404" count="0" type="stmt"/>
        <line num="407" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="408" count="2" type="stmt"/>
        <line num="411" count="2" type="stmt"/>
        <line num="420" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="422" count="1" type="stmt"/>
        <line num="423" count="1" type="stmt"/>
        <line num="424" count="1" type="stmt"/>
        <line num="426" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="427" count="60" type="cond" truecount="1" falsecount="1"/>
        <line num="428" count="60" type="stmt"/>
        <line num="434" count="60" type="cond" truecount="2" falsecount="1"/>
        <line num="435" count="0" type="stmt"/>
        <line num="439" count="1" type="stmt"/>
        <line num="445" count="0" type="stmt"/>
        <line num="447" count="2" type="stmt"/>
        <line num="457" count="1" type="stmt"/>
      </file>
      <file name="mockApi.ts" path="/Users/<USER>/WebstormProjects/er/frontend/src/services/mockApi.ts">
        <metrics statements="73" coveredstatements="57" conditionals="41" coveredconditionals="25" methods="11" coveredmethods="10"/>
        <line num="11" count="2" type="stmt"/>
        <line num="30" count="30" type="stmt"/>
        <line num="33" count="2" type="stmt"/>
        <line num="36" count="7" type="stmt"/>
        <line num="38" count="7" type="stmt"/>
        <line num="40" count="7" type="cond" truecount="2" falsecount="1"/>
        <line num="41" count="0" type="stmt"/>
        <line num="48" count="7" type="cond" truecount="3" falsecount="0"/>
        <line num="49" count="4" type="stmt"/>
        <line num="56" count="3" type="stmt"/>
        <line num="57" count="3" type="cond" truecount="2" falsecount="1"/>
        <line num="58" count="0" type="stmt"/>
        <line num="65" count="3" type="stmt"/>
        <line num="70" count="3" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="86" count="5" type="stmt"/>
        <line num="88" count="5" type="stmt"/>
        <line num="90" count="5" type="cond" truecount="2" falsecount="1"/>
        <line num="91" count="0" type="stmt"/>
        <line num="98" count="5" type="cond" truecount="3" falsecount="0"/>
        <line num="99" count="1" type="stmt"/>
        <line num="106" count="4" type="cond" truecount="3" falsecount="0"/>
        <line num="107" count="1" type="stmt"/>
        <line num="114" count="3" type="cond" truecount="3" falsecount="0"/>
        <line num="115" count="1" type="stmt"/>
        <line num="122" count="2" type="cond" truecount="2" falsecount="1"/>
        <line num="123" count="0" type="stmt"/>
        <line num="130" count="2" type="stmt"/>
        <line num="131" count="2" type="cond" truecount="2" falsecount="1"/>
        <line num="132" count="0" type="stmt"/>
        <line num="139" count="2" type="stmt"/>
        <line num="144" count="2" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="160" count="5" type="stmt"/>
        <line num="162" count="5" type="stmt"/>
        <line num="163" count="5" type="stmt"/>
        <line num="164" count="19" type="stmt"/>
        <line num="167" count="5" type="stmt"/>
        <line num="169" count="8" type="cond" truecount="1" falsecount="1"/>
        <line num="174" count="8" type="stmt"/>
        <line num="178" count="5" type="stmt"/>
        <line num="180" count="5" type="stmt"/>
        <line num="182" count="19" type="cond" truecount="2" falsecount="0"/>
        <line num="183" count="19" type="stmt"/>
        <line num="187" count="5" type="stmt"/>
        <line num="196" count="5" type="stmt"/>
        <line num="198" count="5" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="212" count="7" type="stmt"/>
        <line num="214" count="7" type="stmt"/>
        <line num="215" count="7" type="stmt"/>
        <line num="216" count="7" type="cond" truecount="0" falsecount="1"/>
        <line num="217" count="0" type="stmt"/>
        <line num="218" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="219" count="0" type="stmt"/>
        <line num="221" count="0" type="stmt"/>
        <line num="224" count="7" type="stmt"/>
        <line num="226" count="7" type="stmt"/>
        <line num="227" count="30" type="stmt"/>
        <line num="228" count="30" type="stmt"/>
        <line num="230" count="30" type="stmt"/>
        <line num="231" count="30" type="stmt"/>
        <line num="232" count="30" type="stmt"/>
        <line num="233" count="30" type="stmt"/>
        <line num="235" count="30" type="stmt"/>
        <line num="244" count="7" type="stmt"/>
        <line num="246" count="7" type="stmt"/>
        <line num="251" count="0" type="stmt"/>
        <line num="260" count="6" type="stmt"/>
        <line num="262" count="6" type="stmt"/>
        <line num="270" count="0" type="stmt"/>
        <line num="272" count="0" type="stmt"/>
        <line num="277" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.utils">
      <metrics statements="45" coveredstatements="45" conditionals="16" coveredconditionals="16" methods="7" coveredmethods="7"/>
      <file name="payslipGenerator.ts" path="/Users/<USER>/WebstormProjects/er/frontend/src/utils/payslipGenerator.ts">
        <metrics statements="45" coveredstatements="45" conditionals="16" coveredconditionals="16" methods="7" coveredmethods="7"/>
        <line num="24" count="1" type="stmt"/>
        <line num="34" count="13" type="stmt"/>
        <line num="38" count="13" type="stmt"/>
        <line num="41" count="13" type="stmt"/>
        <line num="43" count="13" type="stmt"/>
        <line num="44" count="13" type="stmt"/>
        <line num="45" count="13" type="stmt"/>
        <line num="47" count="13" type="stmt"/>
        <line num="48" count="138" type="stmt"/>
        <line num="49" count="138" type="stmt"/>
        <line num="52" count="138" type="stmt"/>
        <line num="53" count="138" type="stmt"/>
        <line num="54" count="138" type="stmt"/>
        <line num="55" count="138" type="stmt"/>
        <line num="56" count="138" type="stmt"/>
        <line num="58" count="138" type="stmt"/>
        <line num="71" count="13" type="stmt"/>
        <line num="79" count="13" type="stmt"/>
        <line num="91" count="91" type="stmt"/>
        <line num="92" count="13" type="stmt"/>
        <line num="94" count="13" type="stmt"/>
        <line num="95" count="104" type="cond" truecount="1" falsecount="0"/>
        <line num="96" count="30" type="stmt"/>
        <line num="100" count="13" type="stmt"/>
        <line num="101" count="13" type="stmt"/>
        <line num="104" count="13" type="stmt"/>
        <line num="115" count="13" type="stmt"/>
        <line num="122" count="13" type="stmt"/>
        <line num="125" count="13" type="cond" truecount="2" falsecount="0"/>
        <line num="126" count="9" type="stmt"/>
        <line num="127" count="4" type="cond" truecount="1" falsecount="0"/>
        <line num="128" count="2" type="stmt"/>
        <line num="132" count="13" type="stmt"/>
        <line num="133" count="13" type="cond" truecount="1" falsecount="0"/>
        <line num="134" count="4" type="stmt"/>
        <line num="137" count="13" type="stmt"/>
        <line num="147" count="138" type="stmt"/>
        <line num="150" count="138" type="cond" truecount="4" falsecount="0"/>
        <line num="151" count="102" type="stmt"/>
        <line num="152" count="36" type="cond" truecount="4" falsecount="0"/>
        <line num="153" count="12" type="stmt"/>
        <line num="155" count="24" type="stmt"/>
        <line num="163" count="1" type="stmt"/>
        <line num="213" count="1" type="stmt"/>
        <line num="222" count="1" type="stmt"/>
      </file>
    </package>
  </project>
</coverage>
