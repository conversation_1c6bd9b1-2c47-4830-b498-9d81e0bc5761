
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for All files</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="prettify.css" />
    <link rel="stylesheet" href="base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1>All files</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">70.56% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>700/992</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">58.66% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>281/479</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">70.89% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>151/213</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">70.09% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>675/963</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="src"><a href="src/index.html">src</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="35" class="abs low">0/35</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="36" class="abs low">0/36</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="12" class="abs low">0/12</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="35" class="abs low">0/35</td>
	</tr>

<tr>
	<td class="file medium" data-value="src/components"><a href="src/components/index.html">src/components</a></td>
	<td data-value="76.92" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 76%"></div><div class="cover-empty" style="width: 24%"></div></div>
	</td>
	<td data-value="76.92" class="pct medium">76.92%</td>
	<td data-value="585" class="abs medium">450/585</td>
	<td data-value="70.41" class="pct medium">70.41%</td>
	<td data-value="267" class="abs medium">188/267</td>
	<td data-value="77.63" class="pct medium">77.63%</td>
	<td data-value="152" class="abs medium">118/152</td>
	<td data-value="76.81" class="pct medium">76.81%</td>
	<td data-value="565" class="abs medium">434/565</td>
	</tr>

<tr>
	<td class="file high" data-value="src/data"><a href="src/data/index.html">src/data</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="4" class="abs high">4/4</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="2" class="abs high">2/2</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">1/1</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="3" class="abs high">3/3</td>
	</tr>

<tr>
	<td class="file medium" data-value="src/hooks"><a href="src/hooks/index.html">src/hooks</a></td>
	<td data-value="51" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 51%"></div><div class="cover-empty" style="width: 49%"></div></div>
	</td>
	<td data-value="51" class="pct medium">51%</td>
	<td data-value="100" class="abs medium">51/100</td>
	<td data-value="28.12" class="pct low">28.12%</td>
	<td data-value="32" class="abs low">9/32</td>
	<td data-value="50" class="pct medium">50%</td>
	<td data-value="8" class="abs medium">4/8</td>
	<td data-value="50.5" class="pct medium">50.5%</td>
	<td data-value="99" class="abs medium">50/99</td>
	</tr>

<tr>
	<td class="file medium" data-value="src/services"><a href="src/services/index.html">src/services</a></td>
	<td data-value="66.96" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 66%"></div><div class="cover-empty" style="width: 34%"></div></div>
	</td>
	<td data-value="66.96" class="pct medium">66.96%</td>
	<td data-value="221" class="abs medium">148/221</td>
	<td data-value="52.38" class="pct medium">52.38%</td>
	<td data-value="126" class="abs medium">66/126</td>
	<td data-value="63.63" class="pct medium">63.63%</td>
	<td data-value="33" class="abs medium">21/33</td>
	<td data-value="66.2" class="pct medium">66.2%</td>
	<td data-value="216" class="abs medium">143/216</td>
	</tr>

<tr>
	<td class="file high" data-value="src/utils"><a href="src/utils/index.html">src/utils</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="47" class="abs high">47/47</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="16" class="abs high">16/16</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="7" class="abs high">7/7</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="45" class="abs high">45/45</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-17T19:08:15.145Z
            </div>
        <script src="prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="sorter.js"></script>
        <script src="block-navigation.js"></script>
    </body>
</html>
    