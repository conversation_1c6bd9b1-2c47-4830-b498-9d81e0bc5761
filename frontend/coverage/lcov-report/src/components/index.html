
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/components</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> src/components</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">77.72% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>457/588</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">70.58% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>192/272</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">77.63% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>118/152</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">77.64% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>441/568</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file high" data-value="CalculatorPage.tsx"><a href="CalculatorPage.tsx.html">CalculatorPage.tsx</a></td>
	<td data-value="95.77" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 95%"></div><div class="cover-empty" style="width: 5%"></div></div>
	</td>
	<td data-value="95.77" class="pct high">95.77%</td>
	<td data-value="71" class="abs high">68/71</td>
	<td data-value="69.04" class="pct medium">69.04%</td>
	<td data-value="42" class="abs medium">29/42</td>
	<td data-value="92.85" class="pct high">92.85%</td>
	<td data-value="14" class="abs high">13/14</td>
	<td data-value="95.58" class="pct high">95.58%</td>
	<td data-value="68" class="abs high">65/68</td>
	</tr>

<tr>
	<td class="file medium" data-value="DataClearingTab.tsx"><a href="DataClearingTab.tsx.html">DataClearingTab.tsx</a></td>
	<td data-value="89.83" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 89%"></div><div class="cover-empty" style="width: 11%"></div></div>
	</td>
	<td data-value="89.83" class="pct medium">89.83%</td>
	<td data-value="59" class="abs medium">53/59</td>
	<td data-value="88.57" class="pct medium">88.57%</td>
	<td data-value="35" class="abs medium">31/35</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="7" class="abs high">7/7</td>
	<td data-value="91.37" class="pct high">91.37%</td>
	<td data-value="58" class="abs high">53/58</td>
	</tr>

<tr>
	<td class="file high" data-value="ErrorMessage.tsx"><a href="ErrorMessage.tsx.html">ErrorMessage.tsx</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="11" class="abs high">11/11</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="3" class="abs high">3/3</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="4" class="abs high">4/4</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="10" class="abs high">10/10</td>
	</tr>

<tr>
	<td class="file low" data-value="FinancialProjectionTable.tsx"><a href="FinancialProjectionTable.tsx.html">FinancialProjectionTable.tsx</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="37" class="abs low">0/37</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="14" class="abs low">0/14</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="7" class="abs low">0/7</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="36" class="abs low">0/36</td>
	</tr>

<tr>
	<td class="file medium" data-value="HistoricalDataTable.tsx"><a href="HistoricalDataTable.tsx.html">HistoricalDataTable.tsx</a></td>
	<td data-value="78.87" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 78%"></div><div class="cover-empty" style="width: 22%"></div></div>
	</td>
	<td data-value="78.87" class="pct medium">78.87%</td>
	<td data-value="71" class="abs medium">56/71</td>
	<td data-value="71.42" class="pct medium">71.42%</td>
	<td data-value="28" class="abs medium">20/28</td>
	<td data-value="76.66" class="pct medium">76.66%</td>
	<td data-value="30" class="abs medium">23/30</td>
	<td data-value="81.15" class="pct medium">81.15%</td>
	<td data-value="69" class="abs medium">56/69</td>
	</tr>

<tr>
	<td class="file high" data-value="HistoryPage.tsx"><a href="HistoryPage.tsx.html">HistoryPage.tsx</a></td>
	<td data-value="99.02" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 99%"></div><div class="cover-empty" style="width: 1%"></div></div>
	</td>
	<td data-value="99.02" class="pct high">99.02%</td>
	<td data-value="103" class="abs high">102/103</td>
	<td data-value="89.28" class="pct medium">89.28%</td>
	<td data-value="28" class="abs medium">25/28</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="34" class="abs high">34/34</td>
	<td data-value="98.91" class="pct high">98.91%</td>
	<td data-value="92" class="abs high">91/92</td>
	</tr>

<tr>
	<td class="file high" data-value="LoadingSpinner.tsx"><a href="LoadingSpinner.tsx.html">LoadingSpinner.tsx</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="4" class="abs high">4/4</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="4" class="abs high">4/4</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">1/1</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="4" class="abs high">4/4</td>
	</tr>

<tr>
	<td class="file high" data-value="MilestoneProjectionsTab.tsx"><a href="MilestoneProjectionsTab.tsx.html">MilestoneProjectionsTab.tsx</a></td>
	<td data-value="97.67" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 97%"></div><div class="cover-empty" style="width: 3%"></div></div>
	</td>
	<td data-value="97.67" class="pct high">97.67%</td>
	<td data-value="43" class="abs high">42/43</td>
	<td data-value="93.33" class="pct high">93.33%</td>
	<td data-value="15" class="abs high">14/15</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="11" class="abs high">11/11</td>
	<td data-value="97.67" class="pct high">97.67%</td>
	<td data-value="43" class="abs high">42/43</td>
	</tr>

<tr>
	<td class="file medium" data-value="Navigation.tsx"><a href="Navigation.tsx.html">Navigation.tsx</a></td>
	<td data-value="85.71" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 85%"></div><div class="cover-empty" style="width: 15%"></div></div>
	</td>
	<td data-value="85.71" class="pct medium">85.71%</td>
	<td data-value="14" class="abs medium">12/14</td>
	<td data-value="88.88" class="pct medium">88.88%</td>
	<td data-value="9" class="abs medium">8/9</td>
	<td data-value="77.77" class="pct medium">77.77%</td>
	<td data-value="9" class="abs medium">7/9</td>
	<td data-value="85.71" class="pct medium">85.71%</td>
	<td data-value="14" class="abs medium">12/14</td>
	</tr>

<tr>
	<td class="file low" data-value="ProjectionsChart.tsx"><a href="ProjectionsChart.tsx.html">ProjectionsChart.tsx</a></td>
	<td data-value="36.47" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 36%"></div><div class="cover-empty" style="width: 64%"></div></div>
	</td>
	<td data-value="36.47" class="pct low">36.47%</td>
	<td data-value="85" class="abs low">31/85</td>
	<td data-value="34.28" class="pct low">34.28%</td>
	<td data-value="35" class="abs low">12/35</td>
	<td data-value="20" class="pct low">20%</td>
	<td data-value="20" class="abs low">4/20</td>
	<td data-value="35.71" class="pct low">35.71%</td>
	<td data-value="84" class="abs low">30/84</td>
	</tr>

<tr>
	<td class="file medium" data-value="TooltipInfo.tsx"><a href="TooltipInfo.tsx.html">TooltipInfo.tsx</a></td>
	<td data-value="79.62" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 79%"></div><div class="cover-empty" style="width: 21%"></div></div>
	</td>
	<td data-value="79.62" class="pct medium">79.62%</td>
	<td data-value="54" class="abs medium">43/54</td>
	<td data-value="70" class="pct medium">70%</td>
	<td data-value="20" class="abs medium">14/20</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="6" class="abs high">6/6</td>
	<td data-value="79.62" class="pct medium">79.62%</td>
	<td data-value="54" class="abs medium">43/54</td>
	</tr>

<tr>
	<td class="file high" data-value="WealthAnalysisDisplay.tsx"><a href="WealthAnalysisDisplay.tsx.html">WealthAnalysisDisplay.tsx</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="13" class="abs high">13/13</td>
	<td data-value="75" class="pct medium">75%</td>
	<td data-value="8" class="abs medium">6/8</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="5" class="abs high">5/5</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="13" class="abs high">13/13</td>
	</tr>

<tr>
	<td class="file high" data-value="WealthTrackerForm.tsx"><a href="WealthTrackerForm.tsx.html">WealthTrackerForm.tsx</a></td>
	<td data-value="95.65" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 95%"></div><div class="cover-empty" style="width: 5%"></div></div>
	</td>
	<td data-value="95.65" class="pct high">95.65%</td>
	<td data-value="23" class="abs high">22/23</td>
	<td data-value="83.87" class="pct medium">83.87%</td>
	<td data-value="31" class="abs medium">26/31</td>
	<td data-value="75" class="pct medium">75%</td>
	<td data-value="4" class="abs medium">3/4</td>
	<td data-value="95.65" class="pct high">95.65%</td>
	<td data-value="23" class="abs high">22/23</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-17T19:16:49.801Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    